package org.springcenter.store.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.common.util.HttpUtil;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springcenter.store.cons.AreaIdConstant;
import org.springcenter.store.dto.*;
import org.springcenter.store.entity.SgDisplayArea;
import org.springcenter.store.entity.SgStoreChecklistAnswer;
import org.springcenter.store.entity.SgStoreChecklistAnswerImg;
import org.springcenter.store.entity.SgStoreChecklistAnswerScore;
import org.springcenter.store.mapper.SgDisplayAreaMapper;
import org.springcenter.store.mapper.SgStoreChecklistAnswerImgMapper;
import org.springcenter.store.mapper.SgStoreChecklistAnswerMapper;
import org.springcenter.store.service.SgDisplayAreaService;
import org.springcenter.store.service.SgStoreChecklistAnswerImgService;
import org.springcenter.store.service.SgStoreChecklistAnswerScoreService;
import org.springcenter.store.service.SgStoreChecklistAnswerService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SgStoreChecklistAnswerServiceImpl  extends ServiceImpl<SgStoreChecklistAnswerMapper, SgStoreChecklistAnswer>  implements SgStoreChecklistAnswerService {

    @Autowired
    private SgStoreChecklistAnswerMapper sgStoreChecklistAnswerMapper;

    @Autowired
    private SgStoreChecklistAnswerImgMapper sgStoreChecklistAnswerImgMapper;

    @Autowired
    private SgDisplayAreaMapper sgDisplayAreaMapper;

    @Autowired
    private SgDisplayAreaService sgDisplayAreaService;

    @Autowired
    private SgStoreChecklistAnswerScoreService sgStoreChecklistAnswerScoreService;

    @Autowired
    private SgStoreChecklistAnswerImgService sgStoreChecklistAnswerImgService;

    @Value("${create.task.url}")
    private String createTaskUrl;



    @Override
    public List<SgStoreChecklistAnswerDto> getSubmitStoreChecklistAnswer(SubmitStoreChecklistAnswerReq requestData, Page page) {
        List<SgStoreChecklistAnswerDto> resultList = new ArrayList<>();

        if(requestData.getSgChecklistId() == null  || StringUtils.isBlank(requestData.getStoreId())){
            return resultList;
        }

        // 查询数据
        QueryWrapper<SgStoreChecklistAnswer> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("update_time");
        SgStoreChecklistAnswer sgStoreChecklistAnswer = new SgStoreChecklistAnswer();
        sgStoreChecklistAnswer.setIsDel(IsDeleteEnum.NORMAL.getCode());
        if(StringUtils.isNotBlank(requestData.getTaskId())){
            sgStoreChecklistAnswer.setTaskId(requestData.getTaskId());
        }
        if(requestData.getSgChecklistId() != null){
            sgStoreChecklistAnswer.setSgChecklistId(requestData.getSgChecklistId());
        }
        if(StringUtils.isNotBlank(requestData.getStoreId())){
            sgStoreChecklistAnswer.setStoreId(requestData.getStoreId());
        }
        queryWrapper.setEntity(sgStoreChecklistAnswer);
        // 查询到数据
        List<SgStoreChecklistAnswer> sgStoreChecklistAnswers = sgStoreChecklistAnswerMapper.selectList(queryWrapper);
        resultList = buildSgStoreChecklistAnswerDto(sgStoreChecklistAnswers);
        return resultList;
    }

    public List<SgStoreChecklistAnswerDto> buildSgStoreChecklistAnswerDto(List<SgStoreChecklistAnswer> sgStoreChecklistAnswers) {
        List<SgStoreChecklistAnswerDto> result = new ArrayList<>();
        // 封装img数据
        if(CollectionUtils.isNotEmpty(sgStoreChecklistAnswers)){
            List<Long> ids = sgStoreChecklistAnswers.stream().map(r -> r.getId()).collect(Collectors.toList());
            // 查询img数据
            QueryWrapper<SgStoreChecklistAnswerImg> queryWrapper1 = new QueryWrapper();
            queryWrapper1.in("sg_store_checklist_answer_id",ids);
            queryWrapper1.eq("is_del",0);
            List<SgStoreChecklistAnswerImg> sgStoreChecklistAnswerImgs = sgStoreChecklistAnswerImgMapper.selectList(queryWrapper1);
            Map<Long, List<SgStoreChecklistAnswerImg>> groupBySgStoreChecklistAnswerId = new HashMap<>();
            if(CollectionUtils.isNotEmpty(sgStoreChecklistAnswerImgs)){
                groupBySgStoreChecklistAnswerId = sgStoreChecklistAnswerImgs.stream().collect(Collectors.groupingBy(r -> r.getSgStoreChecklistAnswerId()));
            }

            // 查询评价数据
            QueryWrapper<SgStoreChecklistAnswerScore> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("sg_store_checklist_answer_id",ids);
            queryWrapper.eq("is_del",0);
            List<SgStoreChecklistAnswerScore> list = sgStoreChecklistAnswerScoreService.list(queryWrapper);
            Map<Long, List<SgStoreChecklistAnswerScoreDto>> scoreGroupBySgStoreChecklistAnswerId = new HashMap<>();
            if(CollectionUtils.isNotEmpty(list)){
                scoreGroupBySgStoreChecklistAnswerId = list.stream().map(r->{
                    SgStoreChecklistAnswerScoreDto sgStoreChecklistAnswerScoreDto = new SgStoreChecklistAnswerScoreDto();
                    BeanUtils.copyProperties(r,sgStoreChecklistAnswerScoreDto);
                    return sgStoreChecklistAnswerScoreDto;
                }).collect(Collectors.groupingBy(r -> r.getSgStoreChecklistAnswerId()));
            }


            // 处理数据
            for (SgStoreChecklistAnswer storeChecklistAnswer : sgStoreChecklistAnswers) {
                SgStoreChecklistAnswerDto sgStoreChecklistAnswerDto = new SgStoreChecklistAnswerDto();
                BeanUtils.copyProperties(storeChecklistAnswer,sgStoreChecklistAnswerDto);
                //
                List<SgStoreChecklistAnswerImg> sgStoreChecklistAnswerImgs1 = groupBySgStoreChecklistAnswerId.get(storeChecklistAnswer.getId());
                if(CollectionUtils.isNotEmpty(sgStoreChecklistAnswerImgs1)){
                    sgStoreChecklistAnswerDto.setImgs(sgStoreChecklistAnswerImgs1.stream().map(r->r.getImgUrl()).collect(Collectors.toList()));
                }

                List<SgStoreChecklistAnswerScoreDto> sgStoreChecklistAnswerScoreDtos = scoreGroupBySgStoreChecklistAnswerId.get(storeChecklistAnswer.getId());
                if(CollectionUtils.isNotEmpty(sgStoreChecklistAnswerScoreDtos)){
                    sgStoreChecklistAnswerDto.setSgStoreChecklistAnswerScoreDtos(sgStoreChecklistAnswerScoreDtos);
                }

                result.add(sgStoreChecklistAnswerDto);
            }
        }
        return result;
    }

    @Override
    public List<SgStoreChecklistAnswerDto> list(ListStoreChecklistDto requestData, Page page) {
        com.github.pagehelper.Page<SgStoreChecklistAnswer> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        QueryWrapper<SgStoreChecklistAnswer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",0);
        queryWrapper.orderByDesc("update_time");
        queryWrapper.eq("task_id",requestData.getTaskId());
        if(StringUtils.isNotBlank(requestData.getStoreName())){
            queryWrapper.like("store_name","%"+requestData.getStoreName()+"%");
        }
        if(CollectionUtils.isNotEmpty(requestData.getSgDisplayAreaId())){
            queryWrapper.in("sg_display_area_id",requestData.getSgDisplayAreaId());
        }
        if(StringUtils.isNotBlank(requestData.getStatus())){
            queryWrapper.eq("status",requestData.getStatus());
        }
        if(requestData.getStartUpdateTime() != null){
            queryWrapper.ge("update_time",requestData.getStartUpdateTime());
        }
        if(requestData.getEndUpdateTime() != null){
            queryWrapper.le("update_time",requestData.getEndUpdateTime());
        }
        if(requestData.getIsCorrection() != null){
            queryWrapper.eq("is_correction",requestData.getIsCorrection());
        }
        sgStoreChecklistAnswerMapper.selectList(queryWrapper);
        PageInfo<SgStoreChecklistAnswer> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<SgStoreChecklistAnswer> list1 = pageInfo.getList();
        // 封装内层参数
        List<SgStoreChecklistAnswerDto> list2 = buildSgStoreChecklistAnswerDto(list1);
        return list2;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(SgStoreChecklistAnswerDto requestData, Page page) {
        if(requestData == null){
            return ;
        }
        if(requestData.getId() == null){
            return ;
        }
        Long id = requestData.getId();
        Integer status = requestData.getStatus();
        List<SgStoreChecklistAnswerScoreDto> sgStoreChecklistAnswerScoreDtos = requestData.getSgStoreChecklistAnswerScoreDtos();


        SgStoreChecklistAnswer sgStoreChecklistAnswer = new SgStoreChecklistAnswer();
        sgStoreChecklistAnswer.setId(id);
        sgStoreChecklistAnswer.setStatus(status);
        sgStoreChecklistAnswer.setComment(requestData.getComment());
        sgStoreChecklistAnswer.setUpdateTime(new Date());
        sgStoreChecklistAnswer.setAuditPerson(requestData.getAuditPerson());
        sgStoreChecklistAnswer.setAuditTime(new Date());
        sgStoreChecklistAnswerMapper.updateById(sgStoreChecklistAnswer);

        if(CollectionUtils.isNotEmpty(sgStoreChecklistAnswerScoreDtos)){
            for (SgStoreChecklistAnswerScoreDto sgStoreChecklistAnswerScoreDto : sgStoreChecklistAnswerScoreDtos) {
                SgStoreChecklistAnswerScore update = new SgStoreChecklistAnswerScore();
                update.setId(sgStoreChecklistAnswerScoreDto.getId());
                update.setCommentScore(sgStoreChecklistAnswerScoreDto.getCommentScore());
                update.setUpdateTime(new Date());
                sgStoreChecklistAnswerScoreService.updateById(update);
            }
        }
    }

    @Override
    public CorrectionInfoDto correctionInfo(String taskId, Page page) {
        CorrectionInfoDto correctionInfoDto = new CorrectionInfoDto();

        QueryWrapper<SgStoreChecklistAnswer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del",0);
        queryWrapper.eq("task_id",taskId);
        queryWrapper.eq("is_correction",1);
        queryWrapper.eq("is_create_task",0);
        // 待整改 未下发任务
        List<SgStoreChecklistAnswer> sgStoreChecklistAnswers = sgStoreChecklistAnswerMapper.selectList(queryWrapper);
        // 查询数量和门店总数
        correctionInfoDto.setNotSendTaskUnCorrectionNum(sgStoreChecklistAnswers.size());
        Set<String> collect = sgStoreChecklistAnswers.stream().map(r -> r.getStoreId()).collect(Collectors.toSet());
        correctionInfoDto.setNotSendTaskStoreNum(collect.size());
        correctionInfoDto.setNotSendTaskStoreIds(new ArrayList<>(collect));

        QueryWrapper<SgStoreChecklistAnswer> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("is_del",0);
        queryWrapper2.eq("task_id",taskId);
        queryWrapper2.eq("is_correction",1);
        queryWrapper2.eq("is_create_task",1);
        List<SgStoreChecklistAnswer> sgStoreChecklistAnswers2 = sgStoreChecklistAnswerMapper.selectList(queryWrapper2);
        correctionInfoDto.setAlreadySendTaskUnCorrectionNum(sgStoreChecklistAnswers2.size());
        correctionInfoDto.setAlreadySendTaskStoreNum(sgStoreChecklistAnswers2.stream().map(r->r.getStoreId()).collect(Collectors.toSet()).size());

        return correctionInfoDto;
    }

    @Override
    public void createTask(CreateTaskDto requestData, Page page) {
        //生成整改任务
        String jsonString = JSONObject.toJSONString(requestData);
        Map map = JSONObject.parseObject(jsonString, Map.class);
        log.info("createTask 创建整改任务请求参数 = {}",jsonString);
        String post = HttpUtil.post(createTaskUrl, map);
        log.info("createTask 创建整改任务返回参数 = {}",post);
        Map map1 = JSONObject.parseObject(post, Map.class);
        if(map1.get("code").toString().equals("200")){
            log.info("createTask 创建整改任务成功");
        }else{
            throw  new RuntimeException(map1.get("msg").toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(List<SgStoreChecklistAnswerDto> requestData, Page page) {
        List<SgStoreChecklistAnswer> insertSg = new ArrayList<>();
        List<SgStoreChecklistAnswerImg> insertSgImg = new ArrayList<>();
        List<SgStoreChecklistAnswerScore> insertSgScore = new ArrayList<>();

        // 提交数据
        //验证
        if(CollectionUtils.isEmpty(requestData)){
            return ;
        }

        // 查询所有
        List<Long> collect = requestData.stream().map(r -> r.getSgDisplayAreaId()).collect(Collectors.toList());
        // 插入评价数据  根据数据查询到评价信息  填充到那边
        DisplayAreaListReq displayAreaListReq = new DisplayAreaListReq();
        displayAreaListReq.setIds(collect);
        List<SgDisplayArea> sgDisplayAreas1 = sgDisplayAreaMapper.selectListByReq(displayAreaListReq);
        List<SgDisplayAreaResp> sgDisplayAreaResps = sgDisplayAreaService.buildSgDisplayAreaResp(sgDisplayAreas1);
        // 根据id分组
        Map<Long, List<SgDisplayAreaResp>> collect1 = sgDisplayAreaResps.stream().collect(Collectors.groupingBy(r -> r.getId()));


        // 过滤条件
        for (SgStoreChecklistAnswerDto requestDatum : requestData) {
            if(requestDatum.getSgDisplayAreaId() == null || requestDatum.getSgChecklistId() == null || StringUtils.isBlank(requestDatum.getTaskId())
                    || StringUtils.isBlank(requestDatum.getStoreId())){
                continue;
            }

            // 新增数据
            SgStoreChecklistAnswer sgStoreChecklistAnswer = new SgStoreChecklistAnswer();
            BeanUtils.copyProperties(requestDatum,sgStoreChecklistAnswer);
            sgStoreChecklistAnswer.setCreateTime(new Date());
            sgStoreChecklistAnswer.setUpdateTime(new Date());

            // 插入主表数据
            sgStoreChecklistAnswerMapper.insert(sgStoreChecklistAnswer);

            // 插入图片数据
            List<String> imgs = requestDatum.getImgs();
            if(CollectionUtils.isNotEmpty(imgs)){
                for (String img : imgs) {
                    SgStoreChecklistAnswerImg sgStoreChecklistAnswerImg = new SgStoreChecklistAnswerImg();
                    sgStoreChecklistAnswerImg.setSgStoreChecklistAnswerId(sgStoreChecklistAnswer.getId());
                    sgStoreChecklistAnswerImg.setCreateTime(new Date());
                    sgStoreChecklistAnswerImg.setUpdateTime(new Date());
                    sgStoreChecklistAnswerImg.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    sgStoreChecklistAnswerImg.setImgUrl(img);
                    insertSgImg.add(sgStoreChecklistAnswerImg);
                }
            }

            // 插入评价数据  根据数据查询到评价信息  填充到那边
            List<SgDisplayAreaResp> sgDisplayAreaResps1 = collect1.get(requestDatum.getSgDisplayAreaId());
            if(CollectionUtils.isNotEmpty(sgDisplayAreaResps1)){
                for (SgDisplayAreaResp sgDisplayAreaResp : sgDisplayAreaResps1) {
                    List<SgDisplayAreaScoreResp> sgDisplayAreaScoreRespList = sgDisplayAreaResp.getSgDisplayAreaScoreRespList();
                    for (SgDisplayAreaScoreResp sgDisplayAreaScoreResp : sgDisplayAreaScoreRespList) {
                        // 获取到内部信息
                        SgStoreChecklistAnswerScore sgStoreChecklistAnswerScore = new SgStoreChecklistAnswerScore();
                        sgStoreChecklistAnswerScore.setCreateTime(new Date());
                        sgStoreChecklistAnswerScore.setUpdateTime(new Date());
                        sgStoreChecklistAnswerScore.setSgStoreChecklistAnswerId(sgStoreChecklistAnswer.getId());
                        sgStoreChecklistAnswerScore.setScore(sgDisplayAreaScoreResp.getScore());
                        sgStoreChecklistAnswerScore.setIsDel(IsDeleteEnum.NORMAL.getCode());
                        sgStoreChecklistAnswerScore.setRequireContent(sgDisplayAreaScoreResp.getRequireContent());
                        sgStoreChecklistAnswerScore.setSgDisplayAreaScoreId(sgDisplayAreaScoreResp.getId());
                        insertSgScore.add(sgStoreChecklistAnswerScore);
                    }
                }
            }
        }
        // 插入数据库
        sgStoreChecklistAnswerImgService.saveBatch(insertSgImg);
        sgStoreChecklistAnswerScoreService.saveBatch(insertSgScore);

    }
}
