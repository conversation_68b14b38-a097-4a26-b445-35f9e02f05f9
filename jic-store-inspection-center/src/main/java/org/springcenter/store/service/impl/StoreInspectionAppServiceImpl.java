package org.springcenter.store.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.store.convert.StoreInspectionConvert;
import org.springcenter.store.dto.*;
import org.springcenter.store.entity.*;
import org.springcenter.store.remote.DictionaryService;
import org.springcenter.store.remote.EmployeeService;
import org.springcenter.store.remote.StoreService;
import org.springcenter.store.remote.req.EmployeeStoreRoleReq;
import org.springcenter.store.remote.req.ListStoreReq;
import org.springcenter.store.remote.resp.EmployeeStoreRoleResp;
import org.springcenter.store.remote.resp.ListStoreResp;
import org.springcenter.store.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StoreInspectionAppServiceImpl implements StoreInspectionAppService {

    @Resource
    private StoreInspectionPersonService personService;
    @Resource
    private StoreInspectionChecklistService checklistService;
    @Resource
    private StoreInspectionTemplateService templateService;
    @Resource
    private StoreInspectionTemplateDetailService templateDetailService;
    @Resource
    private StoreService storeService;
    @Resource
    private StoreInspectionReportDetailService reportDetailService;
    @Resource
    private StoreInspectionReportService reportService;
    @Resource
    private StoreInspectionAdjustTaskService adjustTaskService;
    @Resource
    private StoreInspectionAdjustTaskRecordService adjustTaskRecordService;
    @Resource
    private EmployeeService employeeService;

    @Autowired
    @Qualifier("storeTransactionTemplate")
    private TransactionTemplate transactionTemplate;
    @Autowired
    private DictionaryService dictionaryService;

    @Override
    public SiInspectorCheckResp checkInspector(SiInspectorCheckReq req) {
        List<StoreInspectionPerson> list = personService.listUser2CheckList(req.getUserId());
        log.info("巡店人员表获取结果：{}", JSON.toJSONString(list));
        // 如果存在检查表则认为是检查员
        SiInspectorCheckResp resp = new SiInspectorCheckResp();
        resp.setIsInspector(!CollectionUtils.isEmpty(list));
        return resp;
    }

    @Override
    public List<SiChecklistNameResp> listTemplateName(SiChecklistReq req) {
        // 1. 获取用户的所有检查表
        List<StoreInspectionPerson> personList = personService.listUser2CheckList(req.getUserId());
        log.info("检查表表获取结果：{}", JSON.toJSONString(personList));
        if (CollectionUtils.isEmpty(personList)) {
            return new ArrayList<>();
        }

        // 2. 获取所有检查表ID
        List<Long> checkListIds = personList.stream().map(StoreInspectionPerson::getChecklistId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkListIds)) {
            return new ArrayList<>();
        }

        // 3. 查询检查表详情
        LambdaQueryWrapper<StoreInspectionChecklist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StoreInspectionChecklist::getId, checkListIds)
                .eq(StoreInspectionChecklist::getIsDelete, false)
                .eq(StoreInspectionChecklist::getCanUse, true);
        List<StoreInspectionChecklist> checkList = checklistService.list(queryWrapper);

        // 4. 根据门店包筛选
        List<StoreInspectionChecklist> matchedChecklists = new ArrayList<>();
        for (StoreInspectionChecklist checklist : checkList) {
            // 假设有getStorePackageId方法
            Long storePackageId = checklist.getStorePkgId();
            if (storePackageId == null)
                continue;

            // 查询门店包下所有门店
            ListStoreReq listStoreReq = new ListStoreReq();
            listStoreReq.setStorePackageId(storePackageId);
            List<ListStoreResp> stores = storeService.list(listStoreReq);
            if (CollectionUtils.isEmpty(stores))
                continue;

            // 判断storeId是否在门店包中
            boolean matched = stores.stream().anyMatch(store -> Objects.equals(store.getId(), req.getStoreId()));
            if (matched) {
                matchedChecklists.add(checklist);
            }
        }

        // 5. 转换返回
        return StoreInspectionConvert.INSTANCE.toNameResp(matchedChecklists);
    }

    @Override
    public SiChecklistLoadResp loadTemplate(SiChecklistLoadReq req) {
        // 获取检查表信息
        StoreInspectionChecklist checkList = checklistService.getById(req.getChecklistId());
        log.info("检查表快照:{}", JSON.toJSONString(checkList));
        if (checkList == null || checkList.getIsDelete() || !checkList.getCanUse()) {
            log.info("检查表不存在/已删除/不可用，无法处理请重新选择");
            return new SiChecklistLoadResp();
        }
        // 拉取模板、模板明细
        StoreInspectionTemplate template = templateService.getById(checkList.getTemplateId());

        SiChecklistLoadResp resp = StoreInspectionConvert.INSTANCE.toChecklistResp(checkList);

        // 获取检查分类和检查项
        LambdaQueryWrapper<StoreInspectionTemplateDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreInspectionTemplateDetail::getTemplateId, template.getId())
                .eq(StoreInspectionTemplateDetail::getIsDelete, false);
        List<StoreInspectionTemplateDetail> details = templateDetailService.list(queryWrapper);
        log.info("模板明细快照:{}", JSON.toJSONString(details));

        //分别组装检查分类id、检查项id。走远程获取名称
        List<String> categoryIds = details.stream().map(StoreInspectionTemplateDetail::getCheckCategoryId)
                .distinct().collect(Collectors.toList());
        List<String> itemIds = details.stream().map(StoreInspectionTemplateDetail::getCheckDetailId)
                .distinct().collect(Collectors.toList());
        log.info("检查分类id:{}", JSON.toJSONString(categoryIds));
        log.info("检查项id:{}", JSON.toJSONString(itemIds));
        // 转换成map
        Optional.ofNullable(dictionaryService.getEmployeeStoreRole(categoryIds, 1)).ifPresent(categoryMap -> {
            categoryMap.forEach(category -> {
                resp.getCategories().stream().filter(c -> c.getCheckCategoryId().equals(category.getId()))
                        .findFirst().ifPresent(c -> c.setCheckCategoryTitle(category.getItemText()));
            });
        });
        //dictionaryService.getEmployeeStoreRole(itemIds, 2);

        // 按分类分组
        Map<String, List<StoreInspectionTemplateDetail>> categoryMap = details.stream()
                .collect(Collectors.groupingBy(StoreInspectionTemplateDetail::getCheckCategoryId));

        List<SiChecklistLoadResp.SiCategory> categories = new ArrayList<>();
        for (Map.Entry<String, List<StoreInspectionTemplateDetail>> entry : categoryMap.entrySet()) {
            SiChecklistLoadResp.SiCategory category = StoreInspectionConvert.INSTANCE
                    .toCategory(entry.getValue().get(0));
            category.setCheckCategoryId(entry.getKey());

            // 获取分类名称（预留接口）
            String categoryName = getCategoryName(entry.getKey());
            category.setCheckCategoryTitle(categoryName);

            // 转换检查项
            List<SiChecklistLoadResp.SiItem> items = entry.getValue().stream()
                    .map(detail -> {
                        SiChecklistLoadResp.SiItem item = StoreInspectionConvert.INSTANCE.toItem(detail);
                        // 获取检查项名称（预留接口）
                        String itemName = getItemName(detail.getCheckDetailId());
                        item.setCheckDetailTitle(itemName);
                        return item;
                    })
                    .collect(Collectors.toList());

            category.setDetails(items);
            categories.add(category);
        }

        resp.setCategories(categories);
        return resp;
    }

    @Override
    public SiReportSaveResp saveReport(SiReportSaveReq req) {
        return transactionTemplate.execute(status -> {
            try {
                // 1. 保存巡店报告
                StoreInspectionReport report = StoreInspectionConvert.INSTANCE.toReport(req);
                report.setCreatePerson(req.getUserName());
                report.setCreateTime(java.time.LocalDateTime.now());
                report.setUpdatePerson(req.getUserName());
                report.setUpdateTime(java.time.LocalDateTime.now());
                report.setIsDelete(false);
                // 计算总分值
                report.setTotalScore(BigDecimal.ZERO);
                for (SiReportSaveReq.SiCategory category : req.getCategories()) {
                    for (SiReportSaveReq.SiReportDetail detail : category.getDetails()) {
                        report.setTotalScore(report.getTotalScore().add(BigDecimal.valueOf(detail.getScore())));
                    }
                }
                // 计算总得分
                report.setTotalGainScore(BigDecimal.ZERO);
                for (SiReportSaveReq.SiCategory category : req.getCategories()) {
                    for (SiReportSaveReq.SiReportDetail detail : category.getDetails()) {
                        report.setTotalGainScore(report.getTotalGainScore().add(BigDecimal.valueOf(detail.getGainScore())));
                    }
                }
                // 计算总得分率(保留2位小数)
                report.setGainScoreRate(BigDecimal.ZERO);
                if (report.getTotalGainScore().compareTo(BigDecimal.ZERO) > 0) {
                    report.setGainScoreRate(report.getTotalGainScore().divide(report.getTotalScore(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
                }
                reportService.save(report);

                // 2. 保存报告明细
                List<StoreInspectionReportDetail> details = StoreInspectionConvert.INSTANCE
                        .toReportDetailList(req.getCategories());
                for (StoreInspectionReportDetail detail : details) {
                    detail.setReportId(report.getId());
                    detail.setCreatePerson(req.getUserName());
                    detail.setCreateTime(java.time.LocalDateTime.now());
                    detail.setUpdatePerson(req.getUserName());
                    detail.setUpdateTime(java.time.LocalDateTime.now());
                    detail.setIsDelete(false);
                }
                // 批量插入
                reportDetailService.saveBatch(details);

                // 3. 生成整改任务
                List<StoreInspectionAdjustTask> taskList = new ArrayList<>();
                for (StoreInspectionReportDetail detail : details) {
                    if (Boolean.TRUE.equals(detail.getCanAdjust())) {
                        StoreInspectionAdjustTask task = StoreInspectionConvert.INSTANCE
                                .toAdjustTaskFromReportDetail(report, detail);
                        task.setStatus(StoreInspectionAdjustTask.TaskStatusEnum.WAIT_FEEDBACK.getCode());
                        task.setCreatePerson(req.getUserName());
                        task.setCreateTime(java.time.LocalDateTime.now());
                        task.setUpdatePerson(req.getUserName());
                        task.setUpdateTime(java.time.LocalDateTime.now());
                        task.setIsDelete(false);
                        taskList.add(task);
                    }
                }
                if (CollectionUtils.isNotEmpty(taskList)) {
                    adjustTaskService.saveBatch(taskList);
                }
                // 4. 返回
                SiReportSaveResp resp = new SiReportSaveResp();
                resp.setReportId(report.getId());
                return resp;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 获取分类名称（预留接口）
     */
    private String getCategoryName(String categoryId) {
        // TODO: 实现获取分类名称的逻辑
        return "分类" + categoryId;
    }

    /**
     * 获取检查项名称（预留接口）
     */
    private String getItemName(String itemId) {
        // TODO: 实现获取检查项名称的逻辑
        return "检查项" + itemId;
    }

    @Override
    public List<SiPageStoreListResp> listStore(SiPageStoreListReq req, Page page) {
        // 1. 判断是否检查员
        SiInspectorCheckReq checkReq = new SiInspectorCheckReq();
        checkReq.setUserId(req.getUserId());
        SiInspectorCheckResp checkResp = this.checkInspector(checkReq);

        List<Long> storeIds = new ArrayList<>();
        List<StoreInspectionAdjustTask> taskList;

        QueryWrapper<StoreInspectionAdjustTask> wrapper = new QueryWrapper<>();
        wrapper.select("distinct store_id").lambda()
                .eq(StoreInspectionAdjustTask::getIsDelete, false)
                // 输入框模糊搜索 门店名称和门店编码 两个条件。任意一个存在即可
                .and(StringUtils.isNotBlank(req.getQueryParam()), w -> {
                    w.like(StoreInspectionAdjustTask::getStoreName, req.getQueryParam())
                            .or()
                            .like(StoreInspectionAdjustTask::getStoreCode, req.getQueryParam());
                })
                // 反馈状态多选
                .in(CollectionUtils.isNotEmpty(req.getStatusList()), StoreInspectionAdjustTask::getStatus, req.getStatusList());
        // 排序模式
        if (req.getSortMode() == 1) {
            // 待反馈/待整改-任务创建时间倒序
            wrapper.lambda().orderByDesc(StoreInspectionAdjustTask::getCreateTime);
        } else if (req.getSortMode() == 2) {
            // 已反馈/已结束-任务更新时间倒序
            wrapper.lambda().orderByDesc(StoreInspectionAdjustTask::getUpdateTime);
        } else {
            // 默认创建时间倒序
            wrapper.lambda().orderByDesc(StoreInspectionAdjustTask::getCreateTime);
        }

        // 2. 获取门店id列表
        if (Boolean.TRUE.equals(checkResp.getIsInspector())) {
            wrapper.lambda().eq(StoreInspectionAdjustTask::getUserId, req.getUserId());
        } else {
            // 门店人员：查employeeBaseId关联的门店id
            EmployeeStoreRoleReq roleReq = new EmployeeStoreRoleReq();
            roleReq.setEmployeeBaseId(req.getEmployeeBaseId());
            List<EmployeeStoreRoleResp> roles = employeeService.getEmployeeStoreRole(roleReq);
            // 如果获取到的是空，则返回空列表
            if (roles == null || roles.isEmpty()) {
                return new ArrayList<>();
            }
            for (EmployeeStoreRoleResp r : roles) {
                if (r.getStoreId() != null) {
                    storeIds.add(r.getStoreId());
                }
            }
            if (!storeIds.isEmpty()) {
                wrapper.lambda().in(StoreInspectionAdjustTask::getStoreId, storeIds);
            }
        }
        log.info("查询条件:{}", wrapper.getCustomSqlSegment());
        log.info("查询参数:{}", JSON.toJSONString(wrapper.getParamNameValuePairs()));
        com.github.pagehelper.Page<StoreInspectionAdjustTask> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        taskList = adjustTaskService.list(wrapper);
        PageInfo<StoreInspectionAdjustTask> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());

        // 根据ID列表循环查询一下整改任务中的门店信息，每个id查询一个记录即可
        for (StoreInspectionAdjustTask task : taskList) {
            LambdaQueryWrapper<StoreInspectionAdjustTask> taskQuery = new LambdaQueryWrapper<>();
            taskQuery.eq(StoreInspectionAdjustTask::getStoreId, task.getStoreId());
            StoreInspectionAdjustTask adjustTask = adjustTaskService.getOne(taskQuery);
            if (adjustTask != null) {
                task.setStoreName(adjustTask.getStoreName());
                task.setStoreCode(adjustTask.getStoreCode());
            }
        }

        // 组装返回
        List<SiPageStoreListResp> stores = new ArrayList<>();
        if (taskList != null) {
            for (StoreInspectionAdjustTask task : taskList) {
                SiPageStoreListResp info = new SiPageStoreListResp();
                info.setStoreId(task.getStoreId());
                info.setStoreName(task.getStoreName());
                info.setStoreCode(task.getStoreCode());
                stores.add(info);
            }
        }
        return stores;
    }

    @Override
    public List<SiTaskListResp> listTasksByStore(SiTaskListByStoreReq req, Page page) {
        com.github.pagehelper.Page<StoreInspectionAdjustTask> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());

        // 构建查询条件
        QueryWrapper<StoreInspectionAdjustTask> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(StoreInspectionAdjustTask::getIsDelete, false)
                .eq(StoreInspectionAdjustTask::getStoreId, req.getStoreId())
                .in(CollectionUtils.isNotEmpty(req.getStatusList()),
                        StoreInspectionAdjustTask::getStatus, req.getStatusList());

        // 排序模式
        if (req.getSortMode() == 1) {
            // 任务创建时间倒序
            wrapper.lambda().orderByDesc(StoreInspectionAdjustTask::getCreateTime);
        } else if (req.getSortMode() == 2) {
            // 任务更新时间倒序
            wrapper.lambda().orderByDesc(StoreInspectionAdjustTask::getUpdateTime);
        } else {
            // 默认创建时间倒序
            wrapper.lambda().orderByDesc(StoreInspectionAdjustTask::getCreateTime);
        }

        // 执行查询
        List<StoreInspectionAdjustTask> taskList = adjustTaskService.list(wrapper);

        // 设置分页信息
        PageInfo<StoreInspectionAdjustTask> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());

        // 使用MapStruct转换为响应对象
        List<SiTaskListResp> respList = StoreInspectionConvert.INSTANCE.toTaskListRespList(taskList);

        return respList;
    }

    @Override
    public SiTaskDetailResp getTaskDetail(SiTaskDetailReq req) {
        // 1. 查询整改任务基本信息
        StoreInspectionAdjustTask task = adjustTaskService.getById(req.getAdjustTaskId());
        if (task == null || Boolean.TRUE.equals(task.getIsDelete())) {
            throw new RuntimeException("整改任务不存在");
        }

        // 2. 转换基本信息
        SiTaskDetailResp resp = StoreInspectionConvert.INSTANCE.toTaskDetailResp(task);

//        // 3. 查询整改处理记录
//        LambdaQueryWrapper<StoreInspectionAdjustTaskRecord> recordWrapper = new LambdaQueryWrapper<>();
//        recordWrapper.eq(StoreInspectionAdjustTaskRecord::getAdjustTaskId, req.getAdjustTaskId())
//                .eq(StoreInspectionAdjustTaskRecord::getIsDelete, false)
//                .orderByAsc(StoreInspectionAdjustTaskRecord::getCreateTime);
//        List<StoreInspectionAdjustTaskRecord> recordList = adjustTaskRecordService.list(recordWrapper);
//
//        // 4. 转换处理记录
//        List<SiTaskRecordResp> recordRespList = StoreInspectionConvert.INSTANCE.toTaskRecordRespList(recordList);
//        resp.setRecordList(recordRespList);

        return resp;
    }

    @Override
    public List<SiTaskRecordResp> listTaskRecords(SiTaskRecordListReq req, Page page) {
        // 使用PageHelper进行分页
        com.github.pagehelper.Page<StoreInspectionAdjustTaskRecord> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());

        // 构建查询条件
        LambdaQueryWrapper<StoreInspectionAdjustTaskRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreInspectionAdjustTaskRecord::getAdjustTaskId, req.getAdjustTaskId())
                .eq(StoreInspectionAdjustTaskRecord::getIsDelete, false);

        // 如果传入了反馈类型，则按类型过滤
        wrapper.eq(req.getType() != null, StoreInspectionAdjustTaskRecord::getType, req.getType());

        // 按创建时间正序排列
        if (req.getSort() == 1) {
            wrapper.orderByAsc(StoreInspectionAdjustTaskRecord::getId);
        } else {
            wrapper.orderByDesc(StoreInspectionAdjustTaskRecord::getId);
        }

        // 执行查询
        List<StoreInspectionAdjustTaskRecord> recordList = adjustTaskRecordService.list(wrapper);

        // 设置分页信息
        PageInfo<StoreInspectionAdjustTaskRecord> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());

        // 使用MapStruct转换为响应对象
        List<SiTaskRecordResp> respList = StoreInspectionConvert.INSTANCE.toTaskRecordRespList(recordList);

        return respList;
    }

    @Override
    public Long submitTaskFeedback(SiTaskFeedbackReq req) {
        return transactionTemplate.execute(status -> {
            try {
                // 1. 查询整改任务
                StoreInspectionAdjustTask task = adjustTaskService.getById(req.getAdjustTaskId());
                if (task == null || Boolean.TRUE.equals(task.getIsDelete())) {
                    throw new RuntimeException("整改任务不存在");
                }

                Integer currentStatus = task.getStatus();

                // 2. 获取目标状态
                StoreInspectionAdjustTask.TaskStatusEnum targetStatus =
                        StoreInspectionAdjustTask.TaskStatusEnum.getFeedbackTargetStatus(currentStatus);

                if (targetStatus == null) {
                    throw new RuntimeException("当前状态不允许提交反馈");
                }

                // 3. 验证状态变更是否合法
                if (!StoreInspectionAdjustTask.TaskStatusEnum.isValidTransition(currentStatus, targetStatus.getCode())) {
                    throw new RuntimeException("状态变更不合法");
                }

                // 4. 创建反馈记录
                StoreInspectionAdjustTaskRecord record = StoreInspectionConvert.INSTANCE
                        .toFeedbackRecord(req, currentStatus, targetStatus.getCode());
                record.setCreateTime(LocalDateTime.now());
                record.setUpdateTime(LocalDateTime.now());
                record.setIsDelete(false);

                // 保存反馈记录
                adjustTaskRecordService.save(record);

                // 5. 更新任务状态
                StoreInspectionAdjustTask updateTask = new StoreInspectionAdjustTask();
                updateTask.setId(req.getAdjustTaskId());
                updateTask.setStatus(targetStatus.getCode());
                updateTask.setUpdatePerson(req.getCreatePerson());
                updateTask.setUpdateTime(LocalDateTime.now());
                adjustTaskService.updateById(updateTask);

                // 6. 构建响应
                return record.getId();
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    @Override
    public Long auditTask(SiTaskAuditReq req) {
        return transactionTemplate.execute(status -> {
            try {
                // 1. 查询整改任务
                StoreInspectionAdjustTask task = adjustTaskService.getById(req.getAdjustTaskId());
                if (task == null || Boolean.TRUE.equals(task.getIsDelete())) {
                    throw new RuntimeException("整改任务不存在");
                }

                Integer currentStatus = task.getStatus();

                // 2. 获取目标状态
                StoreInspectionAdjustTask.TaskStatusEnum targetStatus =
                        StoreInspectionAdjustTask.TaskStatusEnum.getAuditTargetStatus(currentStatus, req.getResult());

                if (targetStatus == null) {
                    throw new RuntimeException("当前状态不允许审核");
                }

                // 3. 验证状态变更是否合法
                if (!StoreInspectionAdjustTask.TaskStatusEnum.isValidTransition(currentStatus, targetStatus.getCode())) {
                    throw new RuntimeException("状态变更不合法");
                }

                // 4. 创建审核记录
                StoreInspectionAdjustTaskRecord record = StoreInspectionConvert.INSTANCE
                        .toAuditRecord(req, currentStatus, targetStatus.getCode());
                record.setCreateTime(LocalDateTime.now());
                record.setUpdateTime(LocalDateTime.now());
                record.setIsDelete(false);

                // 保存审核记录
                adjustTaskRecordService.save(record);

                // 5. 更新任务状态
                StoreInspectionAdjustTask updateTask = new StoreInspectionAdjustTask();
                updateTask.setId(req.getAdjustTaskId());
                updateTask.setStatus(targetStatus.getCode());
                updateTask.setUpdatePerson(req.getCreatePerson());
                updateTask.setUpdateTime(LocalDateTime.now());
                adjustTaskService.updateById(updateTask);

                // 6. 构建响应
                return record.getId();
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    @Override
    public void closeTask(SiTaskCloseReq req) {
        transactionTemplate.execute(status -> {
            try {
                // 人员是否为检查员
                SiInspectorCheckReq checkReq = new SiInspectorCheckReq();
                checkReq.setUserId(req.getUserId());
                SiInspectorCheckResp checkResp = this.checkInspector(checkReq);
                if (!Boolean.TRUE.equals(checkResp.getIsInspector())) {
                    throw new RuntimeException("只有检查员才能关闭任务");
                }

                // 1. 查询整改任务
                StoreInspectionAdjustTask task = adjustTaskService.getById(req.getAdjustTaskId());
                if (task == null || Boolean.TRUE.equals(task.getIsDelete())) {
                    throw new RuntimeException("整改任务不存在");
                }

                Integer currentStatus = task.getStatus();

                // 2. 检查当前状态是否可以关闭
                if (!StoreInspectionAdjustTask.TaskStatusEnum.canClose(currentStatus)) {
                    throw new RuntimeException("当前状态不允许关闭");
                }

                Integer targetStatus = StoreInspectionAdjustTask.TaskStatusEnum.CLOSED.getCode();

                // 3. 验证状态变更是否合法
                if (!StoreInspectionAdjustTask.TaskStatusEnum.isValidTransition(currentStatus, targetStatus)) {
                    throw new RuntimeException("状态变更不合法");
                }

                // 4. 更新任务状态
                StoreInspectionAdjustTask updateTask = new StoreInspectionAdjustTask();
                updateTask.setId(req.getAdjustTaskId());
                updateTask.setStatus(targetStatus);
                updateTask.setUpdatePerson(req.getUserName());
                updateTask.setUpdateTime(LocalDateTime.now());
                adjustTaskService.updateById(updateTask);
                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    @Override
    public List<SiReportListResp> listReports(SiReportListReq req, Page page) {
        SiInspectorCheckReq checkReq = new SiInspectorCheckReq();
        checkReq.setUserId(req.getUserId());
        SiInspectorCheckResp checkResp = this.checkInspector(checkReq);


        // 构建查询条件
        LambdaQueryWrapper<StoreInspectionReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreInspectionReport::getIsDelete, false)
                .eq(StoreInspectionReport::getUserId, req.getUserId())
                // 输入框模糊搜索 门店名称和门店编码 两个条件。任意一个存在即可
                .and(StringUtils.isNotBlank(req.getQueryParam()), w -> {
                    w.like(StoreInspectionReport::getStoreName, req.getQueryParam())
                            .or()
                            .like(StoreInspectionReport::getStoreCode, req.getQueryParam());
                })
                // 按创建时间倒序排列
                .orderByDesc(StoreInspectionReport::getCreateTime);

        // 2. 获取门店id列表
        if (Boolean.TRUE.equals(checkResp.getIsInspector())) {
            wrapper.eq(StoreInspectionReport::getUserId, req.getUserId());
        } else {
            // 门店人员：查employeeBaseId关联的门店id
            EmployeeStoreRoleReq roleReq = new EmployeeStoreRoleReq();
            roleReq.setEmployeeBaseId(req.getEmployeeBaseId());
            List<EmployeeStoreRoleResp> roles = employeeService.getEmployeeStoreRole(roleReq);
            // 如果获取到的是空，则返回空列表
            if (roles == null || roles.isEmpty()) {
                return new ArrayList<>();
            }
            List<Long> storeIds = new ArrayList<>();
            for (EmployeeStoreRoleResp r : roles) {
                if (r.getStoreId() != null) {
                    storeIds.add(r.getStoreId());
                }
            }
            if (!storeIds.isEmpty()) {
                wrapper.in(StoreInspectionReport::getStoreId, storeIds);
            }
        }
        log.info("查询条件:{}", wrapper.getCustomSqlSegment());
        log.info("查询参数:{}", JSON.toJSONString(wrapper.getParamNameValuePairs()));
        // 使用PageHelper进行分页
        com.github.pagehelper.Page<StoreInspectionReport> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        // 执行查询
        List<StoreInspectionReport> reportList = reportService.list(wrapper);

        // 设置分页信息
        PageInfo<StoreInspectionReport> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());

        // 使用MapStruct转换为响应对象
        List<SiReportListResp> respList = Optional.ofNullable(reportList).orElse(Lists.newArrayList()).stream()
                .map(StoreInspectionConvert.INSTANCE::report2Resp).collect(Collectors.toList());

        return respList;
    }

    @Override
    public SiReportDetailResp getReportDetail(SiReportDetailReq req) {
        // 查询巡店报告基本信息
        StoreInspectionReport report = reportService.getById(req.getReportId());
        if (report == null || Boolean.TRUE.equals(report.getIsDelete())) {
            throw new RuntimeException("巡店报告不存在");
        }

        // 查询报告明细
        LambdaQueryWrapper<StoreInspectionReportDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(StoreInspectionReportDetail::getReportId, req.getReportId())
                .eq(StoreInspectionReportDetail::getIsDelete, false)
                .orderByAsc(StoreInspectionReportDetail::getId);
        List<StoreInspectionReportDetail> detailList = reportDetailService.list(detailWrapper);

        return StoreInspectionConvert.INSTANCE.reportAndDetail2Resp(report, detailList);
    }
}