package org.springcenter.store.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.store.dto.ChecklistReq;
import org.springcenter.store.dto.SgChecklistResp;
import org.springcenter.store.dto.SgStoreChecklistAnswerDto;
import org.springcenter.store.dto.SubmitStoreChecklistAnswerReq;
import org.springcenter.store.service.SgChecklistService;
import org.springcenter.store.service.SgStoreChecklistAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/miniapp/sgStoreChecklistAnswer")
@Api(value = "sgStoreChecklistAnswerApp", tags = "app端提交获取信息等")
public class SgStoreChecklistAnswerAppController {

    @Autowired
    private SgChecklistService sgChecklistService;

    @Autowired
    private SgStoreChecklistAnswerService sgStoreChecklistAnswerService;


    /**
     * 查看当前检查表门店提交的信息
     */
    @ResponseBody
    @PostMapping("/getSubmitStoreChecklistAnswer")
    @ApiOperation(value = "查看当前检查表门店提交的信息  更新时间倒序   或者  根据任务id 查询 已经提交 已经审批的任务信息")
    public ResponseResult<List<SgStoreChecklistAnswerDto>> getSubmitStoreChecklistAnswer(@RequestBody CommonRequest<SubmitStoreChecklistAnswerReq> request){
        Page page = request.getPage();
        List<SgStoreChecklistAnswerDto> result = sgStoreChecklistAnswerService.getSubmitStoreChecklistAnswer(request.getRequestData(),page);
        return ResponseResult.success(result,page);
    }



    @ResponseBody
    @PostMapping("/getChecklistByIds")
    @ApiOperation(value = "根据ids获取检查表信息")
    public ResponseResult<List<SgChecklistResp>> list(@RequestBody CommonRequest<ChecklistReq> request){
        Page page = request.getPage();
        List<SgChecklistResp> result = sgChecklistService.list(request.getRequestData(),page);
        return ResponseResult.success(result,page);
    }




    @ResponseBody
    @PostMapping("/submit")
    @ApiOperation(value = "提交检查表信息")
    public ResponseResult submit(@RequestBody CommonRequest<List<SgStoreChecklistAnswerDto>> request){
        Page page = request.getPage();
        sgStoreChecklistAnswerService.submit(request.getRequestData(),page);
        return ResponseResult.success();
    }


}
