package org.springcenter.store.api;

import com.alibaba.fastjson.JSON;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.store.dto.*;
import org.springcenter.store.service.StoreInspectionAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/app/si")
@Api(value = "SiApp", tags = "超导巡店-移动端")
public class SiAppController {

    @Resource
    private StoreInspectionAppService appService;

    @PostMapping("/checkInspector")
    @ApiOperation("列表页面-是否为检查员")
    public ResponseResult<SiInspectorCheckResp> checkInspector(@RequestBody @Validated SiInspectorCheckReq req) {
        log.info("查询是否为检查员 入参：{}", JSON.toJSONString(req));
        SiInspectorCheckResp resp = appService.checkInspector(req);
        log.info("查询是否为检查员 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

    @PostMapping("/getTemplateName")
    @ApiOperation("发起巡店页面-查询检查表名称")
    public ResponseResult<List<SiChecklistNameResp>> getTemplateName(@RequestBody @Validated SiChecklistReq req) {
        log.info("查询检查表名称 入参：{}", JSON.toJSONString(req));
        List<SiChecklistNameResp> resp = appService.listTemplateName(req);
        log.info("查询检查表名称 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

    @PostMapping("/loadTemplate")
    @ApiOperation("发起巡店页面-加载检查表")
    public ResponseResult<SiChecklistLoadResp> loadTemplate(@RequestBody @Validated SiChecklistLoadReq req) {
        log.info("加载检查表 入参：{}", JSON.toJSONString(req));
        SiChecklistLoadResp resp = appService.loadTemplate(req);
        log.info("加载检查表 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

    @PostMapping("/saveReport")
    @ApiOperation("发起巡店页面-提交报告")
    public ResponseResult<SiReportSaveResp> saveReport(@RequestBody @Validated SiReportSaveReq req) {
        log.info("提交报告 入参：{}", JSON.toJSONString(req));
        SiReportSaveResp resp = appService.saveReport(req);
        log.info("提交报告 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

    @PostMapping("/pageStoreList")
    @ApiOperation("列表页面-查询整改门店")
    public ResponseResult<List<SiPageStoreListResp>> pageStoreList(@RequestBody @Validated CommonRequest<SiPageStoreListReq> req) {
        log.info("查询整改门店 入参：{}", JSON.toJSONString(req));
        List<SiPageStoreListResp> resp = appService.listStore(req.getRequestData(), req.getPage());
        log.info("查询整改门店 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp, req.getPage());
    }

    @PostMapping("/listTasksByStore")
    @ApiOperation("列表页面-查询门店整改任务")
    public ResponseResult<List<SiTaskListResp>> listTasksByStore(@RequestBody @Validated CommonRequest<SiTaskListByStoreReq> req) {
        log.info("查询门店整改任务 入参：{}", JSON.toJSONString(req));
        List<SiTaskListResp> resp = appService.listTasksByStore(req.getRequestData(), req.getPage());
        log.info("查询门店整改任务 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp, req.getPage());
    }

    @PostMapping("/getTaskDetail")
    @ApiOperation("列表页面-查询整改任务详情")
    public ResponseResult<SiTaskDetailResp> getTaskDetail(@RequestBody @Validated SiTaskDetailReq req) {
        log.info("查询整改任务详情 入参：{}", JSON.toJSONString(req));
        SiTaskDetailResp resp = appService.getTaskDetail(req);
        log.info("查询整改任务详情 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

    @PostMapping("/listTaskRecords")
    @ApiOperation("任务详情页面-查询任务整改处理明细列表")
    public ResponseResult<List<SiTaskRecordResp>> listTaskRecords(@RequestBody @Validated CommonRequest<SiTaskRecordListReq> req) {
        log.info("查询任务整改处理明细列表 入参：{}", JSON.toJSONString(req));
        List<SiTaskRecordResp> resp = appService.listTaskRecords(req.getRequestData(), req.getPage());
        log.info("查询任务整改处理明细列表 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp, req.getPage());
    }

    @PostMapping("/submitTaskFeedback")
    @ApiOperation("任务详情页面-提交反馈")
    public ResponseResult<Long> submitTaskFeedback(@RequestBody @Validated SiTaskFeedbackReq req) {
        log.info("提交反馈 入参：{}", JSON.toJSONString(req));
        Long resp = appService.submitTaskFeedback(req);
        log.info("提交反馈 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

    @PostMapping("/auditTask")
    @ApiOperation("任务详情页面-提交审核")
    public ResponseResult<Long> auditTask(@RequestBody @Validated SiTaskAuditReq req) {
        log.info("提交审核 入参：{}", JSON.toJSONString(req));
        Long resp = appService.auditTask(req);
        log.info("提交审核 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

    @PostMapping("/closeTask")
    @ApiOperation("任务详情页面-关闭任务")
    public ResponseResult closeTask(@RequestBody @Validated SiTaskCloseReq req) {
        log.info("关闭任务 入参：{}", JSON.toJSONString(req));
        appService.closeTask(req);
        log.info("关闭任务 ");
        return ResponseResult.success();
    }

    @PostMapping("/listReports")
    @ApiOperation("巡店报告页面-查询报告列表")
    public ResponseResult<List<SiReportListResp>> listReports(@RequestBody @Validated CommonRequest<SiReportListReq> req) {
        log.info("分页查询巡店报告 入参：{}", JSON.toJSONString(req));
        List<SiReportListResp> resp = appService.listReports(req.getRequestData(), req.getPage());
        log.info("分页查询巡店报告 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp, req.getPage());
    }

    @PostMapping("/getReportDetail")
    @ApiOperation("巡店报告页面-查询报告详情")
    public ResponseResult<SiReportDetailResp> getReportDetail(@RequestBody @Validated SiReportDetailReq req) {
        log.info("查询巡店报告详情 入参：{}", JSON.toJSONString(req));
        SiReportDetailResp resp = appService.getReportDetail(req);
        log.info("查询巡店报告详情 回参：{}", JSON.toJSONString(resp));
        return ResponseResult.success(resp);
    }

}
