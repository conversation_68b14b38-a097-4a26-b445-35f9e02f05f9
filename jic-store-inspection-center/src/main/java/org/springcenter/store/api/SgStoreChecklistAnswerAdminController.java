package org.springcenter.store.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.store.dto.CorrectionInfoDto;
import org.springcenter.store.dto.CreateTaskDto;
import org.springcenter.store.dto.ListStoreChecklistDto;
import org.springcenter.store.dto.SgStoreChecklistAnswerDto;
import org.springcenter.store.service.SgStoreChecklistAnswerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/sgStoreChecklistAnswer")
@Api(value = "sgStoreChecklistAnswerAdmin", tags = "admin后台处理提交的信息")
public class SgStoreChecklistAnswerAdminController {

    @Autowired
    private SgStoreChecklistAnswerService sgStoreChecklistAnswerService;

    @ResponseBody
    @PostMapping("/list")
    @ApiOperation(value = "点评列表信息")
    public ResponseResult<List<SgStoreChecklistAnswerDto>> list(@RequestBody CommonRequest<ListStoreChecklistDto> request){
        Page page = request.getPage();
        List<SgStoreChecklistAnswerDto> result = sgStoreChecklistAnswerService.list(request.getRequestData(),page);
        return ResponseResult.success(result,page);
    }


    @ResponseBody
    @PostMapping("/correctionInfo")
    @ApiOperation(value = "整改信息     未下发  已下发信息 仅需要传递taskId")
    public ResponseResult<CorrectionInfoDto> correctionInfo(@RequestBody CommonRequest<String> request){
        Page page = request.getPage();
        CorrectionInfoDto result = sgStoreChecklistAnswerService.correctionInfo(request.getRequestData(),page);
        return ResponseResult.success(result,page);
    }



    @ResponseBody
    @PostMapping("/audit")
    @ApiOperation(value = "审批  审批通过or审批不通过  审批仅需要传递   主键id， 评分id， 状态  评分分数  审核人信息 ")
    public ResponseResult audit(@RequestBody CommonRequest<SgStoreChecklistAnswerDto> request){
        Page page = request.getPage();
        sgStoreChecklistAnswerService.audit(request.getRequestData(),page);
        return ResponseResult.success();
    }


    // 创建任务   仅需要创建 审批不通过的，并且是没有发送过任务的  修改为已经发放任务 自动拼装门店id 根据任务id查询

    @ResponseBody
    @PostMapping("/createTask")
    @ApiOperation(value = "生成整改任务  门店包ids 由  correctionInfo  接口中返回的storeIds 生成临时门店包id")
    public ResponseResult createTask(@RequestBody CommonRequest<CreateTaskDto> request){
        try {
            Page page = request.getPage();
            sgStoreChecklistAnswerService.createTask(request.getRequestData(),page);
            return ResponseResult.success();
        }catch (Exception e){
            return ResponseResult.error(-1,e.getMessage());
        }
    }

}
