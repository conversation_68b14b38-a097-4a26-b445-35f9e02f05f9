package org.springcenter.store.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SgStoreChecklistAnswerDto {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    /**
     * 陈列区域名称
     */
    @ApiModelProperty(value = "陈列区域名称")
    private String sgDisplayAreaName;

    /**
     * 陈列区域主键id
     */
    @ApiModelProperty(value = "陈列区域主键id")
    private Long sgDisplayAreaId;

    /**
     * 检查表主键id
     */
    @ApiModelProperty(value = "检查表主键id")
    private Long sgChecklistId;

    /**
     * 门店id
     */
    @ApiModelProperty(value = "门店id")
    private String storeId;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 反馈人 创建人
     */
    @ApiModelProperty(value = "反馈人 创建人")
    private String createBy;

    /**
     * 反馈人岗位
     */
    @ApiModelProperty(value = "反馈人岗位")
    private String position;

    /**
     * 状态   0 反馈待审核   1  审核通过  2  审核不通过
     */
    @ApiModelProperty(value = "状态   0 反馈待审核   1  审核通过  2  审核不通过")
    private Integer status;

    /**
     * 0 正常  1 删除
     */
    @ApiModelProperty(value = "0 正常  1 删除")
    private Integer isDel;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间  提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    @ApiModelProperty(value = "审核人信息")
    private String auditPerson;

    /**
     * 评语
     */
    @ApiModelProperty(value = "评语")
    private String comment;

    @ApiModelProperty(value = "陈列区域描述 前端提交字段")
    private String content;


    @ApiModelProperty(value = "上次上传的图片信息")
    private List<String> imgs;


    @ApiModelProperty(value = "是否需要整改    0  不需要整改  1需要整改")
    private Integer isCorrection = 0;

    @ApiModelProperty(value = "是否已经创建了任务    0  未创建   1 已创建")
    private Integer isCreateTask = 0;


    @ApiModelProperty(value = "评分信息")
    private List<SgStoreChecklistAnswerScoreDto> SgStoreChecklistAnswerScoreDtos;


}
