package org.springcenter.store.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("分页拉取整改任务门店请求")
public class SiPageStoreListReq {

    @ApiModelProperty(value = "员工基础ID", required = true)
    @NotNull(message = "员工基础ID不能为空")
    private Long employeeBaseId;
    
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty("搜索框查询")
    private String queryParam;

    @ApiModelProperty("反馈状态（支持多个状态组合查询）")
    private List<Integer> statusList;

    @ApiModelProperty("排序模式（1-待反馈/待整改-任务创建时间倒序，2-已反馈/已结束-任务更新时间倒序）")
    private Integer sortMode = 1;
}