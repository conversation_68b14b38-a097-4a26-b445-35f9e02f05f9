package org.springcenter.store.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SubmitStoreChecklistAnswerReq {

    @ApiModelProperty(value = "门店id")
    private String storeId;

    @ApiModelProperty(value = "检查表主键id")
    private Long sgChecklistId;

    @ApiModelProperty(value = "任务id  针对于 已上传 已过期的任务  当传递任务id 的时候  门店id  检查表主键id 均可以不传 ")
    private String taskId;


}
