package org.springcenter.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("分页查询巡店报告请求")
public class SiReportListReq {

    @ApiModelProperty(value = "员工基础ID", required = true)
    @NotNull(message = "员工基础ID不能为空")
    private Long employeeBaseId;
    
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty("搜索框查询")
    private String queryParam;
}
