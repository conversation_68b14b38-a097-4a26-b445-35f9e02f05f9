package org.springcenter.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("巡店报告列表响应")
public class SiReportListResp {
    
    @ApiModelProperty("报告ID")
    private Long id;
    
    @ApiModelProperty("门店ID")
    private Long storeId;
    
    @ApiModelProperty("门店编号")
    private String storeCode;
    
    @ApiModelProperty("门店名称")
    private String storeName;
    
    @ApiModelProperty("总得分")
    private BigDecimal totalGainScore;
    
    @ApiModelProperty("得分率")
    private BigDecimal gainScoreRate;
    
    @ApiModelProperty("整改截止日期")
    private String deadline;
    
    @ApiModelProperty("报告总结")
    private String summary;
    
    @ApiModelProperty("巡店检查表ID")
    private Long checklistId;
    
    @ApiModelProperty("巡店检查表名称")
    private String checklistTitle;
    
    @ApiModelProperty("提交人ID")
    private Long userId;
    
    @ApiModelProperty("创建时间")
    private String createTime;
    
    @ApiModelProperty("创建人")
    private String createPerson;
}
