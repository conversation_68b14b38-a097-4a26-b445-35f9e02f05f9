package org.springcenter.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("巡店报告详情响应")
public class SiReportDetailResp {

    @ApiModelProperty("报告ID")
    private Long id;

    @ApiModelProperty("门店ID")
    private Long storeId;

    @ApiModelProperty("门店编号")
    private String storeCode;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("总得分")
    private BigDecimal totalGainScore;

    @ApiModelProperty("得分率")
    private BigDecimal gainScoreRate;

    @ApiModelProperty("整改截止日期")
    private String deadline;

    @ApiModelProperty("报告总结")
    private String summary;

    @ApiModelProperty("巡店检查表ID")
    private Long checklistId;

    @ApiModelProperty("巡店检查表名称")
    private String checklistTitle;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("创建人")
    private String createPerson;


    @ApiModelProperty(value = "检查分类列表")
    private List<SiCategory> categories;

    @Data
    @ApiModel(value = "检查分类")
    public static class SiCategory {
        @ApiModelProperty(value = "分类ID")
        private String checkCategoryId;

        @ApiModelProperty(value = "分类名称")
        private String checkCategoryTitle;

        @ApiModelProperty(value = "检查项列表")
        private List<SiReportDetail> details;
    }

    @Data
    @ApiModel(value = "检查明细")
    public static class SiReportDetail {
        @ApiModelProperty(value = "检查项ID")
        private String checkDetailId;
        @ApiModelProperty(value = "检查项名称")
        private String checkDetailTitle;
        @ApiModelProperty(value = "检查项标准")
        private String checkDetailStandard;
        @ApiModelProperty(value = "分值")
        private Integer score;
        @ApiModelProperty(value = "评分")
        private Integer gainScore;
        @ApiModelProperty(value = "是否需要整改")
        private Boolean canAdjust;
        @ApiModelProperty(value = "整改问题描述")
        private String adjustContent;
        @ApiModelProperty(value = "图片列表。前端组装的json")
        private String picList;
    }
}
