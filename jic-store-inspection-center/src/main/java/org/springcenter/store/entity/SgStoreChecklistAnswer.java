package org.springcenter.store.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 门店提交审核信息表
 * @TableName sg_store_checklist_answer
 */
@TableName(value = "sg_store_checklist_answer")
public class SgStoreChecklistAnswer implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 陈列区域名称
     */
    @TableField(value = "sg_display_area_name")
    private String sgDisplayAreaName;

    /**
     * 陈列区域主键id
     */
    @TableField(value = "sg_display_area_id")
    private Long sgDisplayAreaId;

    /**
     * 检查表主键id
     */
    @TableField(value = "sg_checklist_id")
    private Long sgChecklistId;

    /**
     * 门店id
     */
    @TableField(value = "store_id")
    private String storeId;

    /**
     * 门店名称
     */
    @TableField(value = "store_name")
    private String storeName;

    /**
     * 反馈人 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 反馈人岗位
     */
    @TableField(value = "position")
    private String position;

    /**
     * 状态   0 反馈待审核   1  审核通过  2  审核不通过
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 0 正常  1 删除
     */
    @TableField(value = "is_del")
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 评语
     */
    @TableField(value = "comment")
    private String comment;

    @TableField(value = "content")
    @ApiModelProperty(value = "陈列区域描述 前端提交字段")
    private String content;

    @ApiModelProperty(value = "是否需要整改    0  不需要整改  1需要整改")
    @TableField("is_correction")
    private Integer isCorrection = 0;

    @ApiModelProperty(value = "是否已经创建了任务    0  未创建   1 已创建")
    @TableField("is_create_task")
    private Integer isCreateTask = 0;


    @ApiModelProperty(value = "审核时间")
    @TableField("audit_time")
    private Date auditTime;

    @ApiModelProperty(value = "审核人信息")
    @TableField("audit_person")
    private String auditPerson;

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditPerson() {
        return auditPerson;
    }

    public void setAuditPerson(String auditPerson) {
        this.auditPerson = auditPerson;
    }

    public Integer getIsCorrection() {
        return isCorrection;
    }

    public void setIsCorrection(Integer isCorrection) {
        this.isCorrection = isCorrection;
    }

    public Integer getIsCreateTask() {
        return isCreateTask;
    }

    public void setIsCreateTask(Integer isCreateTask) {
        this.isCreateTask = isCreateTask;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 任务id
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * 任务id
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 陈列区域名称
     */
    public String getSgDisplayAreaName() {
        return sgDisplayAreaName;
    }

    /**
     * 陈列区域名称
     */
    public void setSgDisplayAreaName(String sgDisplayAreaName) {
        this.sgDisplayAreaName = sgDisplayAreaName;
    }

    /**
     * 陈列区域主键id
     */
    public Long getSgDisplayAreaId() {
        return sgDisplayAreaId;
    }

    /**
     * 陈列区域主键id
     */
    public void setSgDisplayAreaId(Long sgDisplayAreaId) {
        this.sgDisplayAreaId = sgDisplayAreaId;
    }

    /**
     * 检查表主键id
     */
    public Long getSgChecklistId() {
        return sgChecklistId;
    }

    /**
     * 检查表主键id
     */
    public void setSgChecklistId(Long sgChecklistId) {
        this.sgChecklistId = sgChecklistId;
    }

    /**
     * 门店id
     */
    public String getStoreId() {
        return storeId;
    }

    /**
     * 门店id
     */
    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    /**
     * 门店名称
     */
    public String getStoreName() {
        return storeName;
    }

    /**
     * 门店名称
     */
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    /**
     * 反馈人 创建人
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 反馈人 创建人
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 反馈人岗位
     */
    public String getPosition() {
        return position;
    }

    /**
     * 反馈人岗位
     */
    public void setPosition(String position) {
        this.position = position;
    }

    /**
     * 状态   0 反馈待审核   1  审核通过  2  审核不通过
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 状态   0 反馈待审核   1  审核通过  2  审核不通过
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 0 正常  1 删除
     */
    public Integer getIsDel() {
        return isDel;
    }

    /**
     * 0 正常  1 删除
     */
    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 评语
     */
    public String getComment() {
        return comment;
    }

    /**
     * 评语
     */
    public void setComment(String comment) {
        this.comment = comment;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SgStoreChecklistAnswer other = (SgStoreChecklistAnswer) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTaskId() == null ? other.getTaskId() == null : this.getTaskId().equals(other.getTaskId()))
            && (this.getSgDisplayAreaName() == null ? other.getSgDisplayAreaName() == null : this.getSgDisplayAreaName().equals(other.getSgDisplayAreaName()))
            && (this.getSgDisplayAreaId() == null ? other.getSgDisplayAreaId() == null : this.getSgDisplayAreaId().equals(other.getSgDisplayAreaId()))
            && (this.getSgChecklistId() == null ? other.getSgChecklistId() == null : this.getSgChecklistId().equals(other.getSgChecklistId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getStoreName() == null ? other.getStoreName() == null : this.getStoreName().equals(other.getStoreName()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getPosition() == null ? other.getPosition() == null : this.getPosition().equals(other.getPosition()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getIsDel() == null ? other.getIsDel() == null : this.getIsDel().equals(other.getIsDel()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getComment() == null ? other.getComment() == null : this.getComment().equals(other.getComment()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTaskId() == null) ? 0 : getTaskId().hashCode());
        result = prime * result + ((getSgDisplayAreaName() == null) ? 0 : getSgDisplayAreaName().hashCode());
        result = prime * result + ((getSgDisplayAreaId() == null) ? 0 : getSgDisplayAreaId().hashCode());
        result = prime * result + ((getSgChecklistId() == null) ? 0 : getSgChecklistId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getStoreName() == null) ? 0 : getStoreName().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getPosition() == null) ? 0 : getPosition().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getIsDel() == null) ? 0 : getIsDel().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getComment() == null) ? 0 : getComment().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", taskId=").append(taskId);
        sb.append(", sgDisplayAreaName=").append(sgDisplayAreaName);
        sb.append(", sgDisplayAreaId=").append(sgDisplayAreaId);
        sb.append(", sgChecklistId=").append(sgChecklistId);
        sb.append(", storeId=").append(storeId);
        sb.append(", storeName=").append(storeName);
        sb.append(", createBy=").append(createBy);
        sb.append(", position=").append(position);
        sb.append(", status=").append(status);
        sb.append(", isDel=").append(isDel);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", comment=").append(comment);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}