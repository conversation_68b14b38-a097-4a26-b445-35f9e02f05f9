package org.springcenter.store.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 报告明细表
 */
@Data
@TableName("store_inspection_report_detail")
public class StoreInspectionReportDetail {
    
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    @TableField("create_person")
    private String createPerson;
    
    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 修改人
     */
    @TableField("update_person")
    private String updatePerson;
    
    /**
     * 是否删除
     */
    @TableField("is_delete")
    private Boolean isDelete;
    
    /**
     * 报告id
     */
    @TableField("report_id")
    private Long reportId;
    
//    /**
//     * 巡查模板id
//     */
//    @TableField("template_id")
//    private Long templateId;
    
    /**
     * 分类id
     */
    @TableField("check_category_id")
    private String checkCategoryId;
    
    /**
     * 分类名称
     */
    @TableField("check_category_title")
    private String checkCategoryTitle;
    
    /**
     * 检查项id
     */
    @TableField("check_detail_id")
    private String checkDetailId;
    
    /**
     * 检查项名称
     */
    @TableField("check_detail_title")
    private String checkDetailTitle;
    
    /**
     * 检查项标准
     */
    @TableField("check_detail_standard")
    private String checkDetailStandard;
    
    /**
     * 分值
     */
    @TableField("score")
    private Integer score;
    
    /**
     * 获得分数
     */
    @TableField("gain_score")
    private Integer gainScore;
    
    /**
     * 是否整改
     */
    @TableField("can_adjust")
    private Boolean canAdjust;
    
    /**
     * 整改内容
     */
    @TableField("adjust_content")
    private String adjustContent;
    
    /**
     * 文件集合，前端json
     */
    @TableField("pic_list")
    private String picList;
} 